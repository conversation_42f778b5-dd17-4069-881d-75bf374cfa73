import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/logging/app_logger.dart';

/// Supabase authentication data source
/// Handles all Supabase authentication operations
abstract class SupabaseAuthDataSource {
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  });

  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  });

  Future<void> signOut();

  User? getCurrentUser();

  Stream<AuthState> get authStateChanges;

  Future<UserResponse> updateUser({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  });

  Future<void> resetPassword(String email);

  Future<void> resendEmailConfirmation(String email);

  Future<void> deleteUser();

  bool get isAuthenticated;
}

/// Implementation of Supabase authentication data source
class SupabaseAuthDataSourceImpl implements SupabaseAuthDataSource {
  final SupabaseClient _supabase = Supabase.instance.client;

  @override
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      AppLogger.info(
        'Attempting to sign up user with email: $email',
        tag: 'SupabaseAuth',
      );

      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: metadata,
      );

      if (response.user != null) {
        AppLogger.info(
          'User signed up successfully: ${response.user!.id}',
          tag: 'SupabaseAuth',
        );
      } else {
        AppLogger.warning('Sign up response has no user', tag: 'SupabaseAuth');
      }

      return response;
    } catch (error) {
      AppLogger.error('Sign up failed', tag: 'SupabaseAuth', error: error);
      rethrow;
    }
  }

  @override
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      AppLogger.info(
        'Attempting to sign in user with email: $email',
        tag: 'SupabaseAuth',
      );

      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        AppLogger.info(
          'User signed in successfully: ${response.user!.id}',
          tag: 'SupabaseAuth',
        );
      } else {
        AppLogger.warning('Sign in response has no user', tag: 'SupabaseAuth');
      }

      return response;
    } catch (error) {
      AppLogger.error('Sign in failed', tag: 'SupabaseAuth', error: error);
      rethrow;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      AppLogger.info('Attempting to sign out user', tag: 'SupabaseAuth');

      await _supabase.auth.signOut();

      AppLogger.info('User signed out successfully', tag: 'SupabaseAuth');
    } catch (error) {
      AppLogger.error('Sign out failed', tag: 'SupabaseAuth', error: error);
      rethrow;
    }
  }

  @override
  User? getCurrentUser() {
    return _supabase.auth.currentUser;
  }

  @override
  Stream<AuthState> get authStateChanges {
    return _supabase.auth.onAuthStateChange;
  }

  @override
  Future<UserResponse> updateUser({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  }) async {
    try {
      AppLogger.info('Attempting to update user', tag: 'SupabaseAuth');

      final response = await _supabase.auth.updateUser(
        UserAttributes(email: email, password: password, data: data),
      );

      if (response.user != null) {
        AppLogger.info(
          'User updated successfully: ${response.user!.id}',
          tag: 'SupabaseAuth',
        );
      }

      return response;
    } catch (error) {
      AppLogger.error('User update failed', tag: 'SupabaseAuth', error: error);
      rethrow;
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      AppLogger.info(
        'Attempting to reset password for email: $email',
        tag: 'SupabaseAuth',
      );

      await _supabase.auth.resetPasswordForEmail(email);

      AppLogger.info(
        'Password reset email sent successfully',
        tag: 'SupabaseAuth',
      );
    } catch (error) {
      AppLogger.error(
        'Password reset failed',
        tag: 'SupabaseAuth',
        error: error,
      );
      rethrow;
    }
  }

  @override
  Future<void> resendEmailConfirmation(String email) async {
    try {
      AppLogger.info(
        'Attempting to resend email confirmation for: $email',
        tag: 'SupabaseAuth',
      );

      await _supabase.auth.resend(type: OtpType.signup, email: email);

      AppLogger.info(
        'Email confirmation resent successfully',
        tag: 'SupabaseAuth',
      );
    } catch (error) {
      AppLogger.error(
        'Failed to resend email confirmation',
        tag: 'SupabaseAuth',
        error: error,
      );
      rethrow;
    }
  }

  @override
  Future<void> deleteUser() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('No authenticated user to delete');
      }

      AppLogger.info(
        'Attempting to delete user account: ${currentUser.id}',
        tag: 'SupabaseAuth',
      );

      // Note: User deletion from client SDK requires server-side implementation
      // For now, we'll call a custom RPC function or use the REST API
      // This is a placeholder - actual implementation depends on your Supabase setup

      // Option 1: Call a custom RPC function
      await _supabase.rpc('delete_user_account');

      // Option 2: Sign out the user (if server-side deletion is handled separately)
      // await _supabase.auth.signOut();

      AppLogger.info(
        'User account deletion initiated: ${currentUser.id}',
        tag: 'SupabaseAuth',
      );
    } catch (error) {
      AppLogger.error(
        'Failed to delete user account',
        tag: 'SupabaseAuth',
        error: error,
      );
      rethrow;
    }
  }

  @override
  bool get isAuthenticated {
    return _supabase.auth.currentUser != null;
  }
}

/// Exception for Supabase authentication operations
class SupabaseAuthException implements Exception {
  final String message;
  final String? code;

  const SupabaseAuthException(this.message, {this.code});

  @override
  String toString() =>
      'SupabaseAuthException: $message${code != null ? ' (Code: $code)' : ''}';
}
