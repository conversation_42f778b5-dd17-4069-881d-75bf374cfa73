import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/localization/localization_manager.dart';

/// Comprehensive help screen documenting all game features and mechanics
class HelpScreen extends ConsumerWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationManager.helpTitle(ref)),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(context, ref),

            const SizedBox(height: AppConstants.paddingLarge),

            // Game Mechanics
            _buildGameMechanicsSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Special Tiles
            _buildSpecialTilesSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Powerup System
            _buildPowerupSystemSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Game Modes
            _buildGameModesSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Controls
            _buildControlsSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Scoring
            _buildScoringSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // Tips & Strategies
            _buildTipsStrategiesSection(context, ref),

            const SizedBox(height: AppConstants.paddingMedium),

            // FAQ
            _buildFAQSection(context, ref),

            const SizedBox(height: AppConstants.paddingLarge),

            // Contact Support
            _buildContactSection(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.25),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.help_outline,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            LocalizationManager.helpTitle(ref),
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            LocalizationManager.helpDescription(ref),
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGameMechanicsSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpGameMechanics(ref),
      icon: Icons.grid_view,
      items: [
        '${LocalizationManager.helpBoardLayout(ref)}: ${LocalizationManager.helpBoardLayoutDesc(ref)}',
        '${LocalizationManager.helpBasicGameplay(ref)}: ${LocalizationManager.helpBasicGameplayDesc(ref)}',
        '${LocalizationManager.helpKeyboardControls(ref)}: ${LocalizationManager.helpKeyboardControlsDesc(ref)}',
        '${LocalizationManager.helpTileMovement(ref)}: ${LocalizationManager.helpTileMovementDesc(ref)}',
        '${LocalizationManager.helpMergingRules(ref)}: ${LocalizationManager.helpMergingRulesDesc(ref)}',
        '${LocalizationManager.helpWinCondition(ref)}: ${LocalizationManager.helpWinConditionDesc(ref)}',
        '${LocalizationManager.helpGameOver(ref)}: ${LocalizationManager.helpGameOverDesc(ref)}',
      ],
    );
  }

  Widget _buildSpecialTilesSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpSpecialTiles(ref),
      icon: Icons.block,
      items: [
        '${LocalizationManager.helpBlockerTiles(ref)}: ${LocalizationManager.helpBlockerTilesDesc(ref)}',
        LocalizationManager.helpBlockerRules(ref),
        '• ${LocalizationManager.helpBlockerRule1(ref)}',
        '• ${LocalizationManager.helpBlockerRule2(ref)}',
        '• ${LocalizationManager.helpBlockerRule3(ref)}',
      ],
    );
  }

  Widget _buildPowerupSystemSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpPowerupSystem(ref),
      icon: Icons.flash_on,
      items: [
        '${LocalizationManager.helpPowerupUnlock(ref)}: ${LocalizationManager.helpPowerupUnlockDesc(ref)}',
        LocalizationManager.helpAvailablePowerups(ref),
        '• ${LocalizationManager.helpTileDestroyer(ref)}: ${LocalizationManager.helpTileDestroyerDesc(ref)}',
        '• ${LocalizationManager.helpRowClear(ref)}: ${LocalizationManager.helpRowClearDesc(ref)}',
        '• ${LocalizationManager.helpColumnClear(ref)}: ${LocalizationManager.helpColumnClearDesc(ref)}',
        '• ${LocalizationManager.helpValueUpgrade(ref)}: ${LocalizationManager.helpValueUpgradeDesc(ref)}',
        '• ${LocalizationManager.helpTileFreeze(ref)}: ${LocalizationManager.helpTileFreezeDesc(ref)}',
        '${LocalizationManager.helpPowerupUsage(ref)}: ${LocalizationManager.helpPowerupUsageDesc(ref)}',
      ],
    );
  }

  Widget _buildGameModesSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpGameModes(ref),
      icon: Icons.gamepad,
      items: [
        '${LocalizationManager.helpClassicMode(ref)}: ${LocalizationManager.helpClassicModeDesc(ref)}',
        '${LocalizationManager.helpTimeAttackMode(ref)}: ${LocalizationManager.helpTimeAttackModeDesc(ref)}',
        '${LocalizationManager.helpScenicMode(ref)}: ${LocalizationManager.helpScenicModeDesc(ref)}',
        '${LocalizationManager.helpCustomMode(ref)}: ${LocalizationManager.helpCustomModeDesc(ref)}',
      ],
    );
  }

  Widget _buildControlsSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpControls(ref),
      icon: Icons.touch_app,
      items: [
        '${LocalizationManager.helpTouchControls(ref)}: ${LocalizationManager.helpTouchControlsDesc(ref)}',
        '${LocalizationManager.helpKeyboardSupport(ref)}: ${LocalizationManager.helpKeyboardSupportDesc(ref)}',
        '${LocalizationManager.helpPauseControls(ref)}: ${LocalizationManager.helpPauseControlsDesc(ref)}',
      ],
    );
  }

  Widget _buildScoringSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpScoring(ref),
      icon: Icons.score,
      items: [
        '${LocalizationManager.helpScoreDisplay(ref)}: ${LocalizationManager.helpScoreDisplayDesc(ref)}',
        '${LocalizationManager.helpBestScore(ref)}: ${LocalizationManager.helpBestScoreDesc(ref)}',
        '${LocalizationManager.helpStatistics(ref)}: ${LocalizationManager.helpStatisticsDesc(ref)}',
      ],
    );
  }

  Widget _buildTipsStrategiesSection(BuildContext context, WidgetRef ref) {
    return _buildHelpSection(
      context,
      ref,
      title: LocalizationManager.helpTipsStrategies(ref),
      icon: Icons.lightbulb_outline,
      items: [
        '${LocalizationManager.helpCornerStrategy(ref)}: ${LocalizationManager.helpCornerStrategyDesc(ref)}',
        '${LocalizationManager.helpDirectionConsistency(ref)}: ${LocalizationManager.helpDirectionConsistencyDesc(ref)}',
        '${LocalizationManager.helpPowerupTiming(ref)}: ${LocalizationManager.helpPowerupTimingDesc(ref)}',
        '${LocalizationManager.helpBlockerManagement(ref)}: ${LocalizationManager.helpBlockerManagementDesc(ref)}',
      ],
    );
  }

  Widget _buildHelpSection(
    BuildContext context,
    WidgetRef ref, {
    required String title,
    required IconData icon,
    required List<String> items,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusSmall,
                    ),
                  ),
                  child: Icon(
                    icon,
                    size: AppConstants.iconSizeMedium,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ...items.map(
              (item) => Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.paddingSmall,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Text(
                        item,
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(height: 1.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context, WidgetRef ref) {
    final faqs = [
      {
        'question': LocalizationManager.helpFaqHowToPlay(ref),
        'answer': LocalizationManager.helpFaqHowToPlayAnswer(ref),
      },
      {
        'question': LocalizationManager.helpFaqWinCondition(ref),
        'answer': LocalizationManager.helpFaqWinConditionAnswer(ref),
      },
      {
        'question': LocalizationManager.helpFaqPowerups(ref),
        'answer': LocalizationManager.helpFaqPowerupsAnswer(ref),
      },
      {
        'question': LocalizationManager.helpFaqBlockers(ref),
        'answer': LocalizationManager.helpFaqBlockersAnswer(ref),
      },
      {
        'question': LocalizationManager.helpFaqGameModes(ref),
        'answer': LocalizationManager.helpFaqGameModesAnswer(ref),
      },
      {
        'question': LocalizationManager.helpFaqControls(ref),
        'answer': LocalizationManager.helpFaqControlsAnswer(ref),
      },
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.quiz,
                  size: AppConstants.iconSizeMedium,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Text(
                  LocalizationManager.helpFaq(ref),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            ...faqs.map(
              (faq) => ExpansionTile(
                title: Text(
                  faq['question']!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    child: Text(
                      faq['answer']!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            Icon(
              Icons.support_agent,
              size: AppConstants.iconSizeLarge,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              LocalizationManager.helpNeedMoreHelp(ref),
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              LocalizationManager.helpContactSupport(ref),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Implement contact support
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(LocalizationManager.comingSoon(ref))),
                );
              },
              icon: const Icon(Icons.email),
              label: Text(LocalizationManager.contactUs(ref)),
            ),
          ],
        ),
      ),
    );
  }
}
